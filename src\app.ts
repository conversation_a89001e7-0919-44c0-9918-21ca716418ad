// 应用主文件 - 简化版本
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import type { Env } from './types';

// 创建基础 Hono 应用
const app = new Hono<{ Bindings: Env }>();

// 中间件配置
app.use('*', logger());
app.use('*', prettyJSON());
app.use('/api/*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));
  schema: {
    info: {
      title: 'Verify API - 软件许可证验证服务',
      version: '1.0.0',
      description: `
# Verify API 文档

一个面向个人开发者的轻量级软件许可证网络验证服务系统，基于 Cloudflare Workers 环境构建。

## 功能特性

- 🔐 **许可证验证**：支持过期时间、设备限制、功能权限等多种验证策略
- 👥 **分级管理**：超级管理员和普通管理员权限体系
- 📊 **统计分析**：完整的销售统计和使用分析功能
- 🚀 **高性能**：基于 Cloudflare Workers 全球边缘网络
- 💾 **混合存储**：D1 数据库 + KV 缓存的高效存储架构

## 认证方式

大部分管理接口需要 JWT Token 认证，请在请求头中添加：
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## 默认账号

- **超级管理员**：用户名 \`root\`，密码 \`password\`
- **普通管理员**：用户名 \`user\`，密码 \`password\`

## 错误码说明

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
      `,
      contact: {
        name: 'Verify API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'https://verify.your-domain.com',
        description: '生产环境'
      },
      {
        url: 'http://127.0.0.1:8787',
        description: '开发环境'
      }
    ],
    tags: [
      {
        name: '系统监控',
        description: '系统健康检查和状态监控接口'
      },
      {
        name: '许可证验证',
        description: '客户端许可证验证相关接口'
      },
      {
        name: '管理员认证',
        description: '管理员登录和认证相关接口'
      },
      {
        name: '产品管理',
        description: '软件产品管理接口（仅超级管理员）'
      },
      {
        name: '许可证管理',
        description: '许可证生成和管理接口'
      },
      {
        name: '订单管理',
        description: '订单查询和状态管理接口'
      },
      {
        name: '统计分析',
        description: '销售统计和使用分析接口'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Token 认证，格式：Bearer <token>'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  }
});

// 注册文档路由（这些只是为了生成文档，实际处理在其他地方）
openapi.post('/api/admin/login', AdminLogin);
openapi.post('/api/verify', LicenseVerify);
openapi.get('/api/health', HealthCheck);

// 根路径 - 返回 API 信息和文档链接
openapi.get('/', (c) => {
  const userAgent = c.req.header('User-Agent') || '';
  
  // 如果是浏览器访问，返回友好的 HTML 页面
  if (userAgent.includes('Mozilla')) {
    return c.html(`
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify API - 软件许可证验证服务</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
          .container { max-width: 900px; margin: 0 auto; padding: 40px 20px; }
          .card { background: white; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); padding: 40px; margin-bottom: 30px; }
          h1 { color: #333; margin: 0 0 10px 0; font-size: 2.5em; font-weight: 700; }
          .subtitle { color: #666; font-size: 1.2em; margin-bottom: 30px; }
          .status { background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
          .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
          .info-item { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }
          .info-item h3 { margin: 0 0 10px 0; color: #333; }
          .info-item p { margin: 0; color: #666; }
          .credentials { background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107; }
          .credentials h3 { margin: 0 0 15px 0; color: #856404; }
          .cred-item { background: rgba(255,255,255,0.7); padding: 10px; border-radius: 4px; margin: 8px 0; }
          code { background: #e9ecef; padding: 3px 8px; border-radius: 4px; font-family: 'Monaco', 'Menlo', monospace; font-size: 0.9em; }
          .btn-group { text-align: center; margin: 30px 0; }
          .btn { display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #007acc, #005a9e); color: white; text-decoration: none; border-radius: 6px; margin: 8px; font-weight: 500; transition: all 0.3s ease; }
          .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,122,204,0.3); }
          .btn.secondary { background: linear-gradient(135deg, #6c757d, #5a6268); }
          .footer { text-align: center; color: rgba(255,255,255,0.8); margin-top: 40px; }
          .api-endpoints { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .endpoint { font-family: monospace; background: white; padding: 8px 12px; margin: 4px 0; border-radius: 4px; border-left: 3px solid #28a745; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="card">
            <h1>🔐 Verify API</h1>
            <p class="subtitle">软件许可证验证服务 - 基于 Cloudflare Workers</p>
            
            <div class="status">
              <h3>✅ 系统运行正常</h3>
              <p>数据库连接正常 | 缓存服务可用 | API 文档已生成</p>
            </div>
            
            <div class="info-grid">
              <div class="info-item">
                <h3>📋 服务信息</h3>
                <p><strong>版本：</strong>v1.0.0</p>
                <p><strong>环境：</strong>Cloudflare Workers</p>
                <p><strong>数据库：</strong>D1 + KV Cache</p>
              </div>
              <div class="info-item">
                <h3>🚀 性能特性</h3>
                <p><strong>响应时间：</strong>&lt; 200ms</p>
                <p><strong>全球部署：</strong>边缘网络</p>
                <p><strong>高可用性：</strong>99.9%+</p>
              </div>
            </div>
            
            <div class="credentials">
              <h3>🔑 默认登录凭据</h3>
              <div class="cred-item">
                <strong>超级管理员：</strong>用户名 <code>root</code> | 密码 <code>password</code>
              </div>
              <div class="cred-item">
                <strong>普通管理员：</strong>用户名 <code>user</code> | 密码 <code>password</code>
              </div>
            </div>
            
            <div class="api-endpoints">
              <h3>🔗 主要 API 端点</h3>
              <div class="endpoint">POST /api/admin/login - 管理员登录</div>
              <div class="endpoint">POST /api/verify - 许可证验证</div>
              <div class="endpoint">GET /api/health - 健康检查</div>
              <div class="endpoint">GET /docs - Swagger UI 文档</div>
            </div>
            
            <div class="btn-group">
              <a href="/docs" class="btn">📚 Swagger 文档</a>
              <a href="/redoc" class="btn secondary">📖 ReDoc 文档</a>
              <a href="/api/health" class="btn secondary">🔍 健康检查</a>
              <a href="/openapi.json" class="btn secondary">📄 OpenAPI JSON</a>
            </div>
          </div>
          
          <div class="footer">
            <p>Powered by Cloudflare Workers | Built with Hono.js & Chanfana</p>
          </div>
        </div>
      </body>
      </html>
    `);
  }
  
  // API 访问返回 JSON 信息
  return c.json({
    success: true,
    data: {
      service: 'Verify API - 软件许可证验证服务',
      version: '1.0.0',
      status: 'running',
      documentation: {
        swagger: '/docs',
        redoc: '/redoc',
        openapi_json: '/openapi.json'
      },
      endpoints: {
        health: '/api/health',
        verify: '/api/verify',
        admin_login: '/api/admin/login'
      }
    },
    msg: '服务运行正常'
  });
});

export default openapi;
