// Cloudflare Workers 主入口文件
import { OpenAPIHono } from '@hono/zod-openapi';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import type { Env } from './types';

// 导入路由
import adminRoutes from './routes/admin';

// 创建 OpenAPIHono 应用
const app = new OpenAPIHono<{ Bindings: Env }>();

// 中间件配置
app.use('*', logger());
app.use('*', prettyJSON());
app.use('/api/*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 注册路由
app.route('/api/admin', adminRoutes);

// 健康检查接口
app.get('/api/health', async (c) => {
  try {
    const dbCheck = await c.env.DB.prepare('SELECT 1 as test').first();
    await c.env.VERIFY_CACHE.put('health_check', 'ok', { expirationTtl: 60 });

    return c.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: dbCheck ? 'connected' : 'disconnected',
          cache: 'connected',
          api: 'running'
        },
        version: '1.0.0'
      },
      msg: '系统运行正常'
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    return c.json({
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误'
      },
      msg: '系统检查失败'
    }, 500);
  }
});

// 许可证验证接口
app.post('/api/verify', async (c) => {
  try {
    return c.json({
      success: false,
      data: null,
      msg: '许可证验证功能正在开发中'
    }, 501);
  } catch (error) {
    console.error('许可证验证错误:', error);
    return c.json({
      success: false,
      data: null,
      msg: '验证服务暂时不可用'
    }, 500);
  }
});

// 根路径
app.get('/', (c) => {
  return c.json({
    success: true,
    data: {
      service: 'Verify API - 软件许可证验证服务',
      version: '1.0.0',
      status: 'running',
      documentation: {
        swagger: '/docs',
        redoc: '/redoc',
        openapi_json: '/openapi.json'
      },
      endpoints: {
        health: '/api/health',
        verify: '/api/verify',
        admin_login: '/api/admin/login'
      }
    },
    msg: '服务运行正常'
  });
});

// 配置 OpenAPI 文档
app.doc('/openapi.json', {
  openapi: '3.0.0',
  info: {
    version: '1.0.0',
    title: 'Verify API - 软件许可证验证服务',
    description: '一个面向个人开发者的轻量级软件许可证网络验证服务系统'
  },
  tags: [
    {
      name: '管理员认证',
      description: '管理员登录和认证相关接口'
    },
    {
      name: '许可证验证',
      description: '客户端许可证验证相关接口'
    },
    {
      name: '系统监控',
      description: '系统健康检查和状态监控接口'
    }
  ]
});

export default app;
