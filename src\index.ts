// Cloudflare Workers 主入口文件
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { fromHono } from 'chanfana';
import type { Env, ApiResponse } from './types';

// 导入路由
import adminRoutes from './routes/admin';

// 导入 OpenAPI 文档类
import { AdminLogin, LicenseVerify, HealthCheck } from './docs/openapi';

// 创建基础 Hono 应用
const app = new Hono<{ Bindings: Env }>();

// 中间件配置
app.use('*', logger());
app.use('*', prettyJSON());
app.use('/api/*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 创建 OpenAPI 应用用于文档生成
const openapi = fromHono(app, {
  schema: {
    info: {
      title: 'Verify API - 软件许可证验证服务',
      version: '1.0.0',
      description: `
# Verify API 文档

一个面向个人开发者的轻量级软件许可证网络验证服务系统，基于 Cloudflare Workers 环境构建。

## 功能特性

- 🔐 **许可证验证**：支持过期时间、设备限制、功能权限等多种验证策略
- 👥 **分级管理**：超级管理员和普通管理员权限体系
- 📊 **统计分析**：完整的销售统计和使用分析功能
- 🚀 **高性能**：基于 Cloudflare Workers 全球边缘网络
- 💾 **混合存储**：D1 数据库 + KV 缓存的高效存储架构

## 认证方式

大部分管理接口需要 JWT Token 认证，请在请求头中添加：
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## 默认账号

- **超级管理员**：用户名 \`root\`，密码 \`password\`
- **普通管理员**：用户名 \`user\`，密码 \`password\`

## 错误码说明

- **200**: 请求成功
- **400**: 请求参数错误
- **401**: 认证失败或Token无效
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误
      `,
    },
    tags: [
      {
        name: '系统监控',
        description: '系统健康检查和状态监控接口'
      },
      {
        name: '许可证验证',
        description: '客户端许可证验证相关接口'
      },
      {
        name: '管理员认证',
        description: '管理员登录和认证相关接口'
      },
      {
        name: '产品管理',
        description: '软件产品管理接口（仅超级管理员）'
      },
      {
        name: '许可证管理',
        description: '许可证生成和管理接口'
      },
      {
        name: '订单管理',
        description: '订单查询和状态管理接口'
      },
      {
        name: '统计分析',
        description: '销售统计和使用分析接口'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Token 认证，格式：Bearer <token>'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  }
});

// 注册 OpenAPI 文档路由（仅用于生成文档）
openapi.post('/api/admin/login', AdminLogin);
openapi.post('/api/verify', LicenseVerify);
openapi.get('/api/health', HealthCheck);

// 注册实际的API路由
app.route('/api/admin', adminRoutes);

// 健康检查接口
app.get('/api/health', async (c) => {
  try {
    // 检查数据库连接
    const dbCheck = await c.env.DB.prepare('SELECT 1 as test').first();
    
    // 检查KV存储
    const kvCheck = await c.env.VERIFY_CACHE.get('health_check');
    await c.env.VERIFY_CACHE.put('health_check', 'ok', { expirationTtl: 60 });
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: dbCheck ? 'connected' : 'disconnected',
          cache: 'connected',
          api: 'running'
        },
        version: '1.0.0'
      },
      msg: '系统运行正常'
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    return c.json<ApiResponse>({
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误'
      },
      msg: '系统检查失败'
    }, 500);
  }
});

// 许可证验证接口（核心功能）
app.post('/api/verify', async (c) => {
  try {
    // TODO: 实现许可证验证逻辑
    return c.json<ApiResponse>({
      success: false,
      data: null,
      msg: '许可证验证功能正在开发中'
    }, 501);
  } catch (error) {
    console.error('许可证验证错误:', error);
    return c.json<ApiResponse>({
      success: false,
      data: null,
      msg: '验证服务暂时不可用'
    }, 500);
  }
});

// 根路径 - 返回 API 信息
app.get('/', (c) => {
  const userAgent = c.req.header('User-Agent') || '';
  
  // 如果是浏览器访问，返回HTML页面
  if (userAgent.includes('Mozilla')) {
    return c.html(`
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify API - 软件许可证验证服务</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; }
          .card { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
          h1 { color: #2c3e50; margin: 0 0 10px 0; }
          .subtitle { color: #7f8c8d; font-size: 18px; margin-bottom: 30px; }
          .status { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 20px 0; }
          .btn { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px; }
          .btn.secondary { background: #6c757d; }
          .endpoint { font-family: monospace; background: #f8f9fa; padding: 8px 12px; margin: 4px 0; border-radius: 4px; border-left: 3px solid #28a745; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="card">
            <h1>🔐 Verify API</h1>
            <p class="subtitle">软件许可证验证服务 - 基于 Cloudflare Workers</p>
            
            <div class="status">
              <h3>✅ 系统运行正常</h3>
              <p>API 服务已启动 | 文档已生成 | 准备接收请求</p>
            </div>
            
            <h3>🔗 主要 API 端点</h3>
            <div class="endpoint">POST /api/admin/login - 管理员登录</div>
            <div class="endpoint">POST /api/verify - 许可证验证</div>
            <div class="endpoint">GET /api/health - 健康检查</div>
            <div class="endpoint">GET /docs - Swagger UI 文档</div>
            
            <div>
              <a href="/docs" class="btn">📚 Swagger 文档</a>
              <a href="/redoc" class="btn secondary">📖 ReDoc 文档</a>
              <a href="/api/health" class="btn secondary">🔍 健康检查</a>
              <a href="/openapi.json" class="btn secondary">📄 OpenAPI JSON</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `);
  }
  
  // API 访问返回 JSON 信息
  return c.json<ApiResponse>({
    success: true,
    data: {
      service: 'Verify API - 软件许可证验证服务',
      version: '1.0.0',
      status: 'running',
      documentation: {
        swagger: '/docs',
        redoc: '/redoc',
        openapi_json: '/openapi.json'
      },
      endpoints: {
        health: '/api/health',
        verify: '/api/verify',
        admin_login: '/api/admin/login'
      }
    },
    msg: '服务运行正常'
  });
});

// 导出 OpenAPI 应用（包含文档功能）
export default openapi;
